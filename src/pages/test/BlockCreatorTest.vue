<template>
  <q-page class="q-pa-md">
    <div class="row items-center justify-between q-mb-md">
      <div class="text-h4">BlockCreator Test Page</div>
      <div class="row q-gutter-sm">
        <q-btn label="Reset to Default" color="primary" @click="resetToDefaultBlocks" icon="home" />
        <q-btn label="Load Mock Data" color="secondary" @click="resetToMockData" icon="refresh" />
        <q-btn label="Clear All Blocks" color="negative" @click="clearAllBlocks" icon="clear" />
        <q-btn label="Show Block Order" color="info" @click="showBlockOrder" icon="list" />
        <q-btn
          label="Test Deletion"
          color="warning"
          @click="testDeletionValidation"
          icon="bug_report"
        />
        <q-btn
          label="View Assessment JSON"
          color="accent"
          @click="showAssessmentJson"
          icon="code"
        />
      </div>
    </div>

    <q-separator class="q-mb-md" />

    <!-- Test with mock data -->
    <div class="q-mb-md">
      <div class="text-h6 q-mb-sm">
        Current Blocks: {{ blocks.length }} | Sections: {{ uniqueSections }}
      </div>
      <div class="text-body2 text-grey-7 q-mb-sm">
        This page tests the refactored BlockCreator component. It starts with default blocks (1
        header + 1 radio item). You can add, duplicate, delete blocks and test section creation.
      </div>
      <div class="text-body2 text-primary">
        <strong>Section Testing:</strong> Click "Add Section" to create new sections. New sections
        will always be added to the end of the form, regardless of current focus position.
      </div>
      <div class="text-body2 text-info q-mt-sm">
        <strong>Drag & Drop:</strong> Use the three-dot menu (⋮) at the top of each block to drag
        and reorder blocks within the form.
      </div>
      <div class="text-body2 text-accent q-mt-sm">
        <strong>Three-Dot Menu:</strong> Each block has a borderless three-dot menu (⋮) at the top
        that serves as both the drag handle and action menu with duplicate and delete options.
      </div>
      <div class="text-body2 text-positive q-mt-sm">
        <strong>Expected Behavior:</strong> Blocks should maintain their data integrity when
        reordered, and the three-dot menu should provide easy access to block actions.
      </div>
      <div class="text-body2 text-warning q-mt-sm">
        <strong>Deletion Testing:</strong> Use the "Test Deletion" button to validate deletion
        functionality. The system will log all deletion operations and validate data integrity.
        Check the browser console for detailed deletion logs.
      </div>
      <div class="text-body2 text-deep-orange q-mt-sm">
        <strong>Enhanced Validation:</strong> The deletion system now includes pre-deletion
        validation, backend API verification, and post-deletion cleanup validation to ensure no
        orphaned data remains.
      </div>
    </div>

    <!-- BlockCreator Component -->
    <BlockCreator :blocks="blocks" type="evaluate" :assessment-id="1" />
  </q-page>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import BlockCreator from 'src/components/common/blocks/BlockCreator.vue';
import { mockItemBlocks } from 'src/data/mock';
import { defaultBlocks } from 'src/data/defaultBlocks';
import type { ItemBlock } from 'src/types/models';
import { useEvaluateFormStore } from 'src/stores/evaluate/form';
import { Notify } from 'quasar';

// Define component name for keep-alive
defineOptions({
  name: 'block-creator-test',
});

// Reactive blocks data - start with default blocks
const blocks = ref<ItemBlock[]>([...defaultBlocks]);

// Store for testing evaluate functionality
const evaluateFormStore = useEvaluateFormStore();

// Computed properties for testing
const uniqueSections = computed(() => {
  const sectionNumbers = new Set(blocks.value.map((block) => block.section));
  return sectionNumbers.size;
});

// Test functions
const resetToMockData = () => {
  blocks.value = [...mockItemBlocks];
};

const resetToDefaultBlocks = () => {
  blocks.value = [...defaultBlocks];
};

const clearAllBlocks = () => {
  blocks.value = [];
};

const showBlockOrder = () => {
  const blockInfo = blocks.value.map((block, index) => ({
    index: index + 1,
    id: block.id,
    type: block.type,
    sequence: block.sequence,
    section: block.section,
  }));

  console.table(blockInfo);
  alert(`Block Order:\n${JSON.stringify(blockInfo, null, 2)}`);
};

// Enhanced test functions for deletion validation
const testDeletionValidation = () => {
  console.log('🧪 Starting deletion validation tests...');

  // Test 1: Validate current blocks
  console.log('📋 Current blocks before deletion test:', {
    totalBlocks: blocks.value.length,
    blockTypes: blocks.value.map((b) => ({ id: b.id, type: b.type })),
    sections: uniqueSections.value,
  });

  // Test 2: Test validation functions if we have blocks
  if (blocks.value.length > 0) {
    const testBlock = blocks.value[0];
    if (!testBlock) {
      console.log('⚠️ First block is undefined');
      return;
    }

    console.log('🔍 Testing validation for first block:', testBlock);

    // Mock the evaluate store with current blocks for testing
    if (evaluateFormStore.currentAssessment) {
      evaluateFormStore.currentAssessment.itemBlocks = [...blocks.value];
    } else {
      // Create a mock assessment for testing
      evaluateFormStore.currentAssessment = {
        id: 1,
        creatorUserId: 1,
        programId: 1,
        name: 'Test Assessment',
        type: 'EVALUATE',
        createdAt: new Date().toISOString(),
        responseEdit: false,
        status: true,
        totalScore: 0,
        timeout: 0,
        passRatio: 0,
        itemBlocks: [...blocks.value],
      };
    }

    // Test pre-deletion validation
    const deletionValidation = evaluateFormStore.validateBlockDeletion(testBlock.id);
    console.log('✅ Pre-deletion validation result:', deletionValidation);

    // Test ID validation
    const idValidation = evaluateFormStore.validateIds();
    console.log('🆔 ID validation result:', idValidation);

    Notify.create({
      message: 'Deletion validation tests completed. Check console for results.',
      type: 'info',
      position: 'top',
    });
  } else {
    console.log('⚠️ No blocks available for deletion testing');
    Notify.create({
      message: 'No blocks available for deletion testing. Add some blocks first.',
      type: 'warning',
      position: 'top',
    });
  }
};

const showAssessmentJson = () => {
  const assessmentData = {
    currentAssessment: evaluateFormStore.currentAssessment,
    blocks: blocks.value,
    validation: evaluateFormStore.validateIds(),
    metadata: {
      totalBlocks: blocks.value.length,
      uniqueSections: uniqueSections.value,
      blockTypes: blocks.value.reduce(
        (acc, block) => {
          acc[block.type] = (acc[block.type] || 0) + 1;
          return acc;
        },
        {} as Record<string, number>,
      ),
    },
  };

  console.log('📊 Assessment JSON Data:', assessmentData);

  // Create a formatted JSON string for display
  const jsonString = JSON.stringify(assessmentData, null, 2);

  // Show in a dialog or alert (you could also create a proper dialog component)
  const blob = new Blob([jsonString], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = 'assessment-data.json';
  a.click();
  URL.revokeObjectURL(url);

  Notify.create({
    message: 'Assessment JSON downloaded. Also logged to console.',
    type: 'positive',
    position: 'top',
  });
};
</script>

<style scoped>
.q-page {
  max-width: 1200px;
  margin: 0 auto;
}
</style>
