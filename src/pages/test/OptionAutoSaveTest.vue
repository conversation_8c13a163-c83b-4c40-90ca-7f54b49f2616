<template>
  <q-page class="q-pa-md">
    <div class="text-h4 q-mb-md">Option Auto-Save Test Page</div>

    <div class="text-h6 q-mb-md">Test Instructions:</div>
    <ol class="q-mb-lg">
      <li>Click "เพิ่มตัวเลือก" to add new options</li>
      <li>Type in option text fields - should auto-save after 500ms</li>
      <li>Change score values - should auto-save after 500ms</li>
      <li>Watch for "Saving..." indicator during auto-save</li>
      <li>Check browser network tab for API calls</li>
      <li>Remove options using the delete button</li>
    </ol>

    <!-- Test ItemBlock Component -->
    <div class="q-mb-lg">
      <div class="text-h6 q-mb-md">ItemBlock Component Test</div>
      <ItemBlockComponent
        :item-block="testItemBlock"
        type="evaluate"
        @duplicate="onDuplicate"
        @delete="onDelete"
      />
    </div>

    <!-- Test Results Display -->
    <div class="q-mb-lg">
      <div class="text-h6 q-mb-md">Current ItemBlock Data:</div>
      <q-card class="q-pa-md">
        <pre>{{ JSON.stringify(testItemBlock, null, 2) }}</pre>
      </q-card>
    </div>

    <!-- API Call Log -->
    <div class="q-mb-lg">
      <div class="text-h6 q-mb-md">API Call Log:</div>
      <q-card class="q-pa-md">
        <div v-if="apiCalls.length === 0" class="text-grey-6">No API calls yet...</div>
        <div v-for="(call, index) in apiCalls" :key="index" class="q-mb-sm">
          <div class="text-weight-bold">{{ call.timestamp }}</div>
          <div>{{ call.method }} {{ call.url }}</div>
          <div class="text-caption">{{ JSON.stringify(call.payload) }}</div>
        </div>
      </q-card>
    </div>

    <!-- Reset Button -->
    <q-btn color="primary" label="Reset Test Data" @click="resetTestData" class="q-mr-md" />

    <!-- Clear Log Button -->
    <q-btn color="secondary" label="Clear API Log" @click="clearApiLog" />
  </q-page>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import type { ItemBlock } from 'src/types/models';
import ItemBlockComponent from 'src/components/common/blocks/ItemBlockComponent.vue';

// Test data
const testItemBlock = reactive<ItemBlock>({
  id: 999,
  assessmentId: 123,
  sequence: 1,
  section: 1,
  type: 'RADIO',
  isRequired: false,
  questions: [
    {
      id: 1001,
      itemBlockId: 999,
      questionText: 'Test Question - Type here to test auto-save',
      isHeader: true,
      sequence: 1,
      score: 10,
    },
  ],
  options: [
    {
      id: 2001,
      itemBlockId: 999,
      optionText: 'Option 1 - Edit me to test auto-save',
      value: 1,
      sequence: 1,
    },
    {
      id: 2002,
      itemBlockId: 999,
      optionText: 'Option 2 - Edit me too',
      value: 2,
      sequence: 2,
    },
  ],
});

// API call tracking
const apiCalls = ref<
  Array<{
    timestamp: string;
    method: string;
    url: string;
    payload: unknown;
  }>
>([]);

// Mock API interceptor to track calls
const originalFetch = window.fetch;
window.fetch = async (input: RequestInfo | URL, init?: RequestInit) => {
  const url = typeof input === 'string' ? input : (input as Request).url;

  // Track API calls to our endpoints
  if (url.includes('/evaluate/questions/') || url.includes('/evaluate/options/')) {
    apiCalls.value.push({
      timestamp: new Date().toLocaleTimeString(),
      method: init?.method || 'GET',
      url: url,
      payload: init?.body ? JSON.parse(init.body as string) : null,
    });
  }

  // Call original fetch (will likely fail since we don't have real backend)
  try {
    return await originalFetch(input, init);
  } catch {
    // Mock successful response for testing
    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  }
};

// Event handlers
const onDuplicate = () => {
  console.log('Duplicate clicked');
};

const onDelete = () => {
  console.log('Delete clicked');
};

const resetTestData = () => {
  // Reset to original test data
  Object.assign(testItemBlock, {
    id: 999,
    assessmentId: 123,
    sequence: 1,
    section: 1,
    type: 'RADIO',
    isRequired: false,
    questions: [
      {
        id: 1001,
        itemBlockId: 999,
        questionText: 'Test Question - Type here to test auto-save',
        isHeader: true,
        sequence: 1,
        score: 10,
      },
    ],
    options: [
      {
        id: 2001,
        itemBlockId: 999,
        optionText: 'Option 1 - Edit me to test auto-save',
        value: 1,
        sequence: 1,
      },
      {
        id: 2002,
        itemBlockId: 999,
        optionText: 'Option 2 - Edit me too',
        value: 2,
        sequence: 2,
      },
    ],
  });
};

const clearApiLog = () => {
  apiCalls.value = [];
};
</script>

<style scoped>
pre {
  background-color: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
}
</style>
