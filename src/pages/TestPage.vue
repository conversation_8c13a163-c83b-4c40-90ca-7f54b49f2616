<template>
  <q-page class="q-pa-md">
    <div class="text-h4 q-mb-lg">Test Pages</div>

    <div class="row q-gutter-md">
      <q-card class="col-12 col-md-5">
        <q-card-section>
          <div class="text-h6">Option Auto-Save Test</div>
          <div class="text-body2 q-mb-md">
            Test the "Add Option" functionality and auto-save mechanism for ItemBlock components.
          </div>
          <q-btn color="primary" label="Open Test" @click="router.push('/test/option-auto-save')" />
        </q-card-section>
      </q-card>

      <q-card class="col-12 col-md-5">
        <q-card-section>
          <div class="text-h6">Block Creator Test</div>
          <div class="text-body2 q-mb-md">Test the BlockCreator component functionality.</div>
          <q-btn color="primary" label="Open Test" @click="router.push('/test/block-creator')" />
        </q-card-section>
      </q-card>

      <q-card class="col-12 col-md-5">
        <q-card-section>
          <div class="text-h6">Three Dots Demo</div>
          <div class="text-body2 q-mb-md">Demo of the ThreeDots component.</div>
          <q-btn color="primary" label="Open Demo" @click="router.push('/test/three-dots')" />
        </q-card-section>
      </q-card>
    </div>

    <q-separator class="q-my-lg" />

    <div class="text-h6 q-mb-md">HeaderBlock Auto-Save Test</div>
    <div v-for="i in 3" :key="i" class="q-mb-md">
      <HeaderBlock @blur="handleAutoSave" />
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';
import HeaderBlock from 'src/components/common/HeaderBlock.vue';

const router = useRouter();

function handleAutoSave(fieldType: string, content: string) {
  // Your auto-save logic here
  console.log(`Auto-saving ${fieldType}: ${content}`);
  // e.g., saveQuiz(), or call an API directly
}
</script>

<style scoped></style>
